---
export interface Props {
  height?: string;
  className?: string;
}

const { height = "500px", className = "" } = Astro.props;
---

<!-- Leaflet CSS and JS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<!-- Leaflet.heat plugin for smooth heatmap rendering -->
<script src="https://unpkg.com/leaflet.heat@0.2.0/dist/leaflet-heat.js"></script>


<div class={`aurora-map-container relative ${className}`}>
  <!-- Map Controls -->
  <div class="absolute top-4 left-4 z-[1000] flex flex-col gap-2">
    <!-- Polar Region Toggle -->
    <div class="bg-dark-800/90 backdrop-blur-lg border border-dark-600 rounded-lg p-3">
      <div class="flex items-center gap-2 mb-2">
        <span class="text-sm font-medium text-gray-300">Polar Region</span>
      </div>
      <div class="flex gap-1">
        <button
          id="north-pole-btn"
          class="polar-toggle-btn active px-3 py-1 text-xs rounded-md transition-all duration-200 bg-aurora-blue/20 text-aurora-blue border border-aurora-blue/30"
        >
          🌌 North
        </button>
        <button
          id="south-pole-btn"
          class="polar-toggle-btn px-3 py-1 text-xs rounded-md transition-all duration-200 bg-dark-700 text-gray-400 border border-dark-600 hover:bg-dark-600"
        >
          🌠 South
        </button>
      </div>
    </div>

    <!-- Fullscreen Toggle -->
    <button
      id="fullscreen-btn"
      class="bg-dark-800/90 backdrop-blur-lg border border-dark-600 rounded-lg p-3 hover:bg-dark-700/90 transition-all duration-200 group"
      title="Toggle Fullscreen"
    >
      <svg class="w-5 h-5 text-gray-300 group-hover:text-white transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
      </svg>
    </button>
  </div>

  <!-- Status Display -->
  <div class="absolute top-4 right-4 z-[1000] flex flex-col gap-2">
    <div id="map-status" class="bg-dark-800/90 backdrop-blur-lg border border-dark-600 rounded-lg px-3 py-2 text-sm">
      <span class="text-gray-300">Initializing...</span>
    </div>

    <!-- Retry Button (hidden by default) -->
    <button
      id="retry-btn"
      class="hidden bg-aurora-blue/20 hover:bg-aurora-blue/30 border border-aurora-blue/30 rounded-lg px-3 py-2 text-sm text-aurora-blue transition-all duration-200"
      title="Retry loading aurora data"
    >
      🔄 Retry
    </button>
  </div>

  <!-- Map Container -->
  <div
    id="aurora-map"
    class="w-full rounded-lg overflow-hidden border border-dark-600"
    style={`height: ${height}`}
  ></div>

  <!-- Data Info Panel -->
  <div class="mt-4 bg-dark-800/80 backdrop-blur-lg border border-dark-600 rounded-lg p-4">
    <div id="aurora-data-info" class="text-sm text-gray-300">
      <div class="flex items-center gap-2 mb-2">
        <div class="w-2 h-2 bg-aurora-green rounded-full animate-pulse"></div>
        <span class="font-medium">Aurora Data</span>
      </div>
      <div id="data-stats" class="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
        <div>
          <span class="text-gray-400">Total Points:</span>
          <span id="total-points" class="ml-1 text-white">-</span>
        </div>
        <div>
          <span class="text-gray-400">Northern:</span>
          <span id="northern-points" class="ml-1 text-aurora-blue">-</span>
        </div>
        <div>
          <span class="text-gray-400">Southern:</span>
          <span id="southern-points" class="ml-1 text-aurora-purple">-</span>
        </div>
        <div>
          <span class="text-gray-400">Updated:</span>
          <span id="update-time" class="ml-1 text-white">-</span>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .aurora-map-container {
    position: relative;
  }

  .polar-toggle-btn.active {
    background: rgba(33, 150, 243, 0.2) !important;
    color: #2196F3 !important;
    border-color: rgba(33, 150, 243, 0.3) !important;
  }

  .polar-toggle-btn:not(.active):hover {
    background: rgba(45, 52, 54, 1) !important;
    color: rgba(255, 255, 255, 0.8) !important;
  }

  /* Fullscreen styles */
  .aurora-map-container.fullscreen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999 !important;
    background: #09090f !important;
    padding: 1rem !important;
  }

  .aurora-map-container.fullscreen #aurora-map {
    height: calc(100vh - 8rem) !important;
  }

  /* Leaflet popup customization */
  :global(.leaflet-popup-content-wrapper) {
    background: rgba(18, 20, 23, 0.95) !important;
    color: white !important;
    border-radius: 8px !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(87, 90, 97, 0.3) !important;
  }

  :global(.leaflet-popup-tip) {
    background: rgba(18, 20, 23, 0.95) !important;
  }

  :global(.leaflet-popup-content) {
    margin: 12px 16px !important;
    font-size: 13px !important;
  }

  /* Aurora tooltip styling */
  :global(.aurora-tooltip) {
    font-family: 'Inter', system-ui, sans-serif !important;
    line-height: 1.4 !important;
  }

  :global(.aurora-tooltip strong) {
    color: #ffffff !important;
  }

  :global(.aurora-tooltip .text-gray-400) {
    color: #9ca3af !important;
    font-size: 11px !important;
  }

  /* Leaflet control styling */
  :global(.leaflet-control-layers) {
    background: rgba(18, 20, 23, 0.9) !important;
    color: white !important;
    border-radius: 8px !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(87, 90, 97, 0.3) !important;
  }

  :global(.leaflet-control-layers-toggle) {
    background-image: none !important;
    width: 36px !important;
    height: 36px !important;
  }

  :global(.leaflet-control-layers-toggle::after) {
    content: '🗂️' !important;
    font-size: 16px !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
  }

  :global(.leaflet-control-layers label) {
    color: white !important;
    font-weight: 400 !important;
  }

  /* Loading animation */
  .loading-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
</style>

<script>
  // Leaflet is loaded globally from CDN
  declare const L: any;

  // Global variables
  let map: L.Map;
  let northernLayer: any = null; // Heatmap layer
  let southernLayer: any = null; // Heatmap layer
  let layerControl: L.Control.Layers;
  let currentPolarView: 'north' | 'south' = 'north';
  let isFullscreen = false;

  // Store raw data for tooltip functionality
  let northernData: AuroraPoint[] = [];
  let southernData: AuroraPoint[] = [];

  // Aurora data interface
  interface AuroraPoint {
    longitude: number;
    latitude: number;
    intensity: number;
  }

  interface AuroraData {
    northern: AuroraPoint[];
    southern: AuroraPoint[];
  }

  interface ApiResponse {
    coordinates: [number, number, number][];
    'Observation Time'?: string;
    'Forecast Time'?: string;
  }

  // Convert from NOAA's 0-360 system to standard -180/180 for mapping
  function getCords180(long360: number, lat360: number): [number, number] {
    const lon180 = long360 > 180 ? long360 - 360 : long360;
    const lat180 = lat360 > 180 ? lat360 - 360 : lat360;
    return [lon180, lat180];
  }

  // Note: Individual color mapping functions removed as we now use
  // heatmap gradients for smooth color transitions

  // Process aurora data from API
  function processAuroraData(data: ApiResponse): AuroraData {
    if (!data || !data.coordinates) return { northern: [], southern: [] };

    const northernData: AuroraPoint[] = [];
    const southernData: AuroraPoint[] = [];

    for (const coord of data.coordinates) {
      const [longitude360, latitude360, intensity] = coord;
      const [longitude, latitude] = getCords180(longitude360, latitude360);

      if (intensity > 0) {
        const point: AuroraPoint = { longitude, latitude, intensity };

        if (latitude >= 0) {
          northernData.push(point);
        } else {
          southernData.push(point);
        }
      }
    }

    return { northern: northernData, southern: southernData };
  }

  // Create heatmap gradient for hemisphere
  function createHeatmapGradient(hemisphere: 'northern' | 'southern'): {[key: number]: string} {
    if (hemisphere === 'northern') {
      // Northern Aurora: Blue-green gradient
      return {
        0.0: 'rgba(0, 0, 0, 0)',
        0.1: 'rgba(0, 180, 80, 0.1)',
        0.3: 'rgba(0, 255, 100, 0.3)',
        0.5: 'rgba(0, 255, 150, 0.5)',
        0.7: 'rgba(0, 200, 255, 0.7)',
        1.0: 'rgba(0, 255, 255, 0.9)'
      };
    } else {
      // Southern Aurora: Purple-pink gradient
      return {
        0.0: 'rgba(0, 0, 0, 0)',
        0.1: 'rgba(180, 100, 150, 0.1)',
        0.3: 'rgba(255, 150, 150, 0.3)',
        0.5: 'rgba(255, 100, 200, 0.5)',
        0.7: 'rgba(200, 0, 255, 0.7)',
        1.0: 'rgba(255, 0, 255, 0.9)'
      };
    }
  }

  // Create aurora heatmap layer for hemisphere
  function createAuroraHeatmapLayer(hemisphereData: AuroraPoint[], hemisphere: 'northern' | 'southern'): any {
    if (!hemisphereData || hemisphereData.length === 0) return null;

    // Convert aurora data to heatmap format: [lat, lng, intensity]
    const heatmapData: [number, number, number][] = hemisphereData.map(point => [
      point.latitude,
      point.longitude,
      Math.min(point.intensity / 20, 1) // Normalize intensity to 0-1 range
    ]);

    // Create heatmap layer with custom gradient
    const heatmapLayer = L.heatLayer(heatmapData, {
      radius: 35, // Radius of each data point
      blur: 25,   // Blur factor
      maxZoom: 18,
      max: 1.0,   // Maximum intensity value
      gradient: createHeatmapGradient(hemisphere)
    });

    return heatmapLayer;
  }

  // Find nearest aurora data point for tooltip functionality
  function findNearestAuroraPoint(lat: number, lng: number, hemisphere: 'northern' | 'southern'): AuroraPoint | null {
    const data = hemisphere === 'northern' ? northernData : southernData;
    if (!data || data.length === 0) return null;

    let nearest: AuroraPoint | null = null;
    let minDistance = Infinity;

    for (const point of data) {
      const distance = Math.sqrt(
        Math.pow(lat - point.latitude, 2) + Math.pow(lng - point.longitude, 2)
      );

      if (distance < minDistance && distance < 5) { // Within 5 degrees
        minDistance = distance;
        nearest = point;
      }
    }

    return nearest;
  }

  // Setup map interactions for tooltips
  function setupMapInteractions() {
    if (!map) return;

    // Create a popup for displaying aurora information
    const popup = L.popup({
      closeButton: true,
      autoClose: false,
      closeOnEscapeKey: true,
      className: 'aurora-popup'
    });

    // Handle map clicks for aurora information
    map.on('click', (e: any) => {
      const { lat, lng } = e.latlng;

      // Check both hemispheres for nearby aurora data
      const northernPoint = findNearestAuroraPoint(lat, lng, 'northern');
      const southernPoint = findNearestAuroraPoint(lat, lng, 'southern');

      // Choose the closest point
      let closestPoint = null;
      let hemisphere = '';

      if (northernPoint && southernPoint) {
        const northDistance = Math.sqrt(
          Math.pow(lat - northernPoint.latitude, 2) + Math.pow(lng - northernPoint.longitude, 2)
        );
        const southDistance = Math.sqrt(
          Math.pow(lat - southernPoint.latitude, 2) + Math.pow(lng - southernPoint.longitude, 2)
        );

        if (northDistance <= southDistance) {
          closestPoint = northernPoint;
          hemisphere = 'northern';
        } else {
          closestPoint = southernPoint;
          hemisphere = 'southern';
        }
      } else if (northernPoint) {
        closestPoint = northernPoint;
        hemisphere = 'northern';
      } else if (southernPoint) {
        closestPoint = southernPoint;
        hemisphere = 'southern';
      }

      if (closestPoint) {
        const content = `
          <div class="aurora-tooltip">
            <strong>${hemisphere === 'northern' ? '🌌 Aurora Borealis' : '🌠 Aurora Australis'}</strong><br>
            <strong>Intensity:</strong> ${closestPoint.intensity.toFixed(1)}<br>
            <strong>Location:</strong> ${closestPoint.latitude.toFixed(2)}°, ${closestPoint.longitude.toFixed(2)}°<br>
            <small class="text-gray-400">Click elsewhere to close</small>
          </div>
        `;

        popup.setLatLng([closestPoint.latitude, closestPoint.longitude])
          .setContent(content)
          .openOn(map);
      }
    });
  }

  // Aurora data API endpoints (primary and fallbacks)
  const AURORA_ENDPOINTS = [
    'https://services.swpc.noaa.gov/json/ovation_aurora_latest.json',
    'https://nodeapi.knockdream.com/skydata/ovation_aurora_latest.json'
  ];

  // Fetch aurora data with fallback endpoints
  async function fetchAuroraData(): Promise<ApiResponse | null> {
    let lastError: Error | null = null;

    for (let i = 0; i < AURORA_ENDPOINTS.length; i++) {
      const endpoint = AURORA_ENDPOINTS[i];
      try {
        updateStatus(`Fetching aurora data${i > 0 ? ` (trying backup source ${i + 1})` : ''}...`);

        // Create timeout controller for older browsers
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

        const response = await fetch(endpoint, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
          },
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        // Validate data structure
        if (!data || !data.coordinates || !Array.isArray(data.coordinates)) {
          throw new Error('Invalid data format received from API');
        }

        console.log(`Successfully fetched aurora data from: ${endpoint}`);
        return data;

      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        console.warn(`Failed to fetch from ${endpoint}:`, lastError.message);

        // If this isn't the last endpoint, continue to next one
        if (i < AURORA_ENDPOINTS.length - 1) {
          continue;
        }
      }
    }

    // All endpoints failed
    console.error('All aurora data endpoints failed. Last error:', lastError);
    return null;
  }

  // Update status display
  function updateStatus(message: string, type: 'info' | 'success' | 'error' = 'info') {
    const statusEl = document.getElementById('map-status');
    if (statusEl) {
      statusEl.className = `bg-dark-800/90 backdrop-blur-lg border border-dark-600 rounded-lg px-3 py-2 text-sm ${
        type === 'success' ? 'border-aurora-green/30' :
        type === 'error' ? 'border-aurora-red/30' : 'border-dark-600'
      }`;
      statusEl.innerHTML = `<span class="${
        type === 'success' ? 'text-aurora-green' :
        type === 'error' ? 'text-aurora-red' : 'text-gray-300'
      }">${message}</span>`;
    }
  }

  // Update data statistics
  function updateDataStats(data: ApiResponse, hemisphereData: AuroraData) {
    const totalPointsEl = document.getElementById('total-points');
    const northernPointsEl = document.getElementById('northern-points');
    const southernPointsEl = document.getElementById('southern-points');
    const updateTimeEl = document.getElementById('update-time');

    if (totalPointsEl) totalPointsEl.textContent = data.coordinates.length.toString();
    if (northernPointsEl) northernPointsEl.textContent = hemisphereData.northern.length.toString();
    if (southernPointsEl) southernPointsEl.textContent = hemisphereData.southern.length.toString();
    if (updateTimeEl) {
      const time = data['Observation Time'] || data['Forecast Time'] || 'Unknown';
      updateTimeEl.textContent = time;
    }
  }

  // Switch polar view
  function switchPolarView(view: 'north' | 'south') {
    currentPolarView = view;

    // Update button states
    const northBtn = document.getElementById('north-pole-btn');
    const southBtn = document.getElementById('south-pole-btn');

    if (northBtn && southBtn) {
      if (view === 'north') {
        northBtn.classList.add('active');
        southBtn.classList.remove('active');
        // Focus on northern regions
        map.setView([75, -100], 3);
      } else {
        southBtn.classList.add('active');
        northBtn.classList.remove('active');
        // Focus on southern regions
        map.setView([-75, 0], 3);
      }
    }
  }

  // Toggle fullscreen mode
  function toggleFullscreen() {
    const container = document.querySelector('.aurora-map-container') as HTMLElement;
    if (!container) return;

    isFullscreen = !isFullscreen;

    if (isFullscreen) {
      container.classList.add('fullscreen');
      document.body.style.overflow = 'hidden';
    } else {
      container.classList.remove('fullscreen');
      document.body.style.overflow = '';
    }

    // Trigger map resize after DOM update
    setTimeout(() => {
      if (map) {
        map.invalidateSize();
      }
    }, 100);
  }

  // Initialize the aurora map
  async function initAuroraMap() {
    try {
      updateStatus('Initializing map...');

      // Initialize map
      map = L.map('aurora-map').setView([65, -100], 3);

      // Add dark tile layer
      L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {
        attribution: '&copy; OpenStreetMap contributors &copy; CARTO',
        subdomains: 'abcd',
        maxZoom: 19
      }).addTo(map);

      updateStatus('Fetching aurora data...');

      // Fetch and process data
      const data = await fetchAuroraData();
      if (!data) {
        updateStatus('⚠️ Aurora data temporarily unavailable. Please try again later.', 'error');

        // Show empty map with informational message
        const infoEl = document.getElementById('aurora-data-info');
        if (infoEl) {
          infoEl.innerHTML = `
            <div class="flex items-center gap-2 mb-2">
              <div class="w-2 h-2 bg-aurora-red rounded-full"></div>
              <span class="font-medium text-aurora-red">Data Unavailable</span>
            </div>
            <p class="text-sm text-gray-400">
              Aurora forecast data is currently unavailable. This may be due to:
            </p>
            <ul class="text-xs text-gray-400 mt-2 space-y-1">
              <li>• Temporary server maintenance</li>
              <li>• Network connectivity issues</li>
              <li>• API service interruption</li>
            </ul>
            <p class="text-xs text-gray-300 mt-2">
              The map will automatically retry when you refresh the page.
            </p>
          `;
        }

        // Show retry button
        const retryBtn = document.getElementById('retry-btn');
        if (retryBtn) {
          retryBtn.classList.remove('hidden');
        }

        // Still show the map, just without aurora data
        return;
      }

      const hemisphereData = processAuroraData(data);

      // Store raw data for tooltip functionality
      northernData = hemisphereData.northern;
      southernData = hemisphereData.southern;

      // Update data statistics
      updateDataStats(data, hemisphereData);

      // Create heatmap layers
      northernLayer = createAuroraHeatmapLayer(hemisphereData.northern, 'northern');
      southernLayer = createAuroraHeatmapLayer(hemisphereData.southern, 'southern');

      // Setup layer control
      layerControl = L.control.layers({}, {}, {
        position: 'topright',
        collapsed: false
      }).addTo(map);

      // Add layers to control and map
      if (northernLayer) {
        layerControl.addOverlay(northernLayer, '🌌 Aurora Borealis (Northern)');
        northernLayer.addTo(map);
      }

      if (southernLayer) {
        layerControl.addOverlay(southernLayer, '🌠 Aurora Australis (Southern)');
        southernLayer.addTo(map);
      }

      // Fit bounds to show aurora activity based on data points
      const allPoints: L.LatLng[] = [];
      if (northernData.length > 0) {
        northernData.forEach(point => allPoints.push(L.latLng(point.latitude, point.longitude)));
      }
      if (southernData.length > 0) {
        southernData.forEach(point => allPoints.push(L.latLng(point.latitude, point.longitude)));
      }

      if (allPoints.length > 0) {
        const bounds = L.latLngBounds(allPoints);
        map.fitBounds(bounds, { padding: [20, 20] });
      }

      // Add click event for tooltips
      setupMapInteractions();

      updateStatus('✅ Aurora map loaded successfully!', 'success');

    } catch (error) {
      console.error('Map initialization failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      updateStatus(`❌ Map Error: ${errorMessage}`, 'error');

      // Show detailed error information
      const infoEl = document.getElementById('aurora-data-info');
      if (infoEl) {
        infoEl.innerHTML = `
          <div class="flex items-center gap-2 mb-2">
            <div class="w-2 h-2 bg-aurora-red rounded-full"></div>
            <span class="font-medium text-aurora-red">Map Initialization Failed</span>
          </div>
          <p class="text-sm text-gray-400 mb-2">
            ${errorMessage}
          </p>
          <p class="text-xs text-gray-300">
            Please refresh the page to try again. If the problem persists, check your internet connection.
          </p>
        `;
      }
    }
  }

  // Retry loading aurora data
  async function retryLoadAuroraData() {
    const retryBtn = document.getElementById('retry-btn');
    if (retryBtn) {
      retryBtn.classList.add('hidden');
    }

    // Clear existing layers
    if (northernLayer) {
      map.removeLayer(northernLayer);
      northernLayer = null;
    }
    if (southernLayer) {
      map.removeLayer(southernLayer);
      southernLayer = null;
    }

    // Clear layer control
    if (layerControl) {
      map.removeControl(layerControl);
    }

    // Try to load data again
    try {
      const data = await fetchAuroraData();
      if (data) {
        const hemisphereData = processAuroraData(data);

        // Store raw data for tooltip functionality
        northernData = hemisphereData.northern;
        southernData = hemisphereData.southern;

        updateDataStats(data, hemisphereData);

        // Create new heatmap layers
        northernLayer = createAuroraHeatmapLayer(hemisphereData.northern, 'northern');
        southernLayer = createAuroraHeatmapLayer(hemisphereData.southern, 'southern');

        // Setup layer control
        layerControl = L.control.layers({}, {}, {
          position: 'topright',
          collapsed: false
        }).addTo(map);

        // Add layers
        if (northernLayer) {
          layerControl.addOverlay(northernLayer, '🌌 Aurora Borealis (Northern)');
          northernLayer.addTo(map);
        }

        if (southernLayer) {
          layerControl.addOverlay(southernLayer, '🌠 Aurora Australis (Southern)');
          southernLayer.addTo(map);
        }

        // Fit bounds based on data points
        const allPoints: L.LatLng[] = [];
        if (northernData.length > 0) {
          northernData.forEach(point => allPoints.push(L.latLng(point.latitude, point.longitude)));
        }
        if (southernData.length > 0) {
          southernData.forEach(point => allPoints.push(L.latLng(point.latitude, point.longitude)));
        }

        if (allPoints.length > 0) {
          const bounds = L.latLngBounds(allPoints);
          map.fitBounds(bounds, { padding: [20, 20] });
        }

        // Re-setup map interactions for tooltips
        setupMapInteractions();

        updateStatus('✅ Aurora data loaded successfully!', 'success');
      } else {
        updateStatus('⚠️ Still unable to load aurora data', 'error');
        if (retryBtn) {
          retryBtn.classList.remove('hidden');
        }
      }
    } catch (error) {
      console.error('Retry failed:', error);
      updateStatus('❌ Retry failed', 'error');
      if (retryBtn) {
        retryBtn.classList.remove('hidden');
      }
    }
  }

  // Setup event listeners
  function setupEventListeners() {
    // Polar region toggle buttons
    const northBtn = document.getElementById('north-pole-btn');
    const southBtn = document.getElementById('south-pole-btn');
    const fullscreenBtn = document.getElementById('fullscreen-btn');
    const retryBtn = document.getElementById('retry-btn');

    if (northBtn) {
      northBtn.addEventListener('click', () => switchPolarView('north'));
    }

    if (southBtn) {
      southBtn.addEventListener('click', () => switchPolarView('south'));
    }

    if (fullscreenBtn) {
      fullscreenBtn.addEventListener('click', toggleFullscreen);
    }

    if (retryBtn) {
      retryBtn.addEventListener('click', retryLoadAuroraData);
    }

    // Escape key to exit fullscreen
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape' && isFullscreen) {
        toggleFullscreen();
      }
    });
  }

  // Wait for Leaflet to load and initialize
  function waitForLeafletAndInit() {
    if (typeof L !== 'undefined') {
      setupEventListeners();
      initAuroraMap();
    } else {
      // Wait a bit more for Leaflet to load
      setTimeout(waitForLeafletAndInit, 100);
    }
  }

  // Initialize when DOM is loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', waitForLeafletAndInit);
  } else {
    waitForLeafletAndInit();
  }
</script>
